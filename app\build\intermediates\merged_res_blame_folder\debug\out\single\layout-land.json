[{"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-mergeDebugResources-43:\\layout-land\\fragment_recording_list.xml", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\layout-land\\fragment_recording_list.xml"}, {"merged": "com.intensivereading.app-mergeDebugResources-43:/layout-land/fragment_main.xml", "source": "com.intensivereading.app-main-46:/layout-land/fragment_main.xml"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-mergeDebugResources-43:\\layout-land\\fragment_main.xml", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\layout-land\\fragment_main.xml"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-mergeDebugResources-43:\\layout-land\\fragment_detail.xml", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\layout-land\\fragment_detail.xml"}, {"merged": "com.intensivereading.app-mergeDebugResources-43:/layout-land/fragment_recording_list.xml", "source": "com.intensivereading.app-main-46:/layout-land/fragment_recording_list.xml"}]