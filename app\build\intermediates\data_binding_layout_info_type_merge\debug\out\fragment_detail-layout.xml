<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_detail" modulePackage="com.intensivereading.app" filePath="app\src\main\res\layout\fragment_detail.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_detail_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="182" endOffset="51"/></Target><Target id="@+id/backButton" view="ImageButton"><Expressions/><location startLine="11" startOffset="4" endLine="22" endOffset="51"/></Target><Target id="@+id/pageIndicator" view="TextView"><Expressions/><location startLine="25" startOffset="4" endLine="38" endOffset="51"/></Target><Target id="@+id/viewPager" view="androidx.viewpager2.widget.ViewPager2"><Expressions/><location startLine="41" startOffset="4" endLine="48" endOffset="51"/></Target><Target id="@+id/bottomControlBar" view="LinearLayout"><Expressions/><location startLine="51" startOffset="4" endLine="109" endOffset="18"/></Target><Target id="@+id/previousButton" view="ImageButton"><Expressions/><location startLine="65" startOffset="8" endLine="74" endOffset="46"/></Target><Target id="@+id/playButton" view="ImageButton"><Expressions/><location startLine="77" startOffset="8" endLine="85" endOffset="46"/></Target><Target id="@+id/recordButton" view="ImageButton"><Expressions/><location startLine="88" startOffset="8" endLine="96" endOffset="46"/></Target><Target id="@+id/nextButton" view="ImageButton"><Expressions/><location startLine="99" startOffset="8" endLine="107" endOffset="46"/></Target><Target id="@+id/recordingOverlay" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="112" startOffset="4" endLine="180" endOffset="55"/></Target><Target id="@+id/recordingTimeText" view="TextView"><Expressions/><location startLine="166" startOffset="20" endLine="172" endOffset="49"/></Target></Targets></Layout>