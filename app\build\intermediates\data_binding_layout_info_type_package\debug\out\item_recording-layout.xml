<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_recording" modulePackage="com.intensivereading.app" filePath="app\src\main\res\layout\item_recording.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_recording_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="93" endOffset="35"/></Target><Target id="@+id/statusIcon" view="TextView"><Expressions/><location startLine="18" startOffset="8" endLine="30" endOffset="61"/></Target><Target id="@+id/titleText" view="TextView"><Expressions/><location startLine="33" startOffset="8" endLine="45" endOffset="55"/></Target><Target id="@+id/startTimeText" view="TextView"><Expressions/><location startLine="48" startOffset="8" endLine="60" endOffset="65"/></Target><Target id="@+id/durationText" view="TextView"><Expressions/><location startLine="63" startOffset="8" endLine="75" endOffset="69"/></Target><Target id="@+id/playButton" view="ImageButton"><Expressions/><location startLine="78" startOffset="8" endLine="89" endOffset="61"/></Target></Targets></Layout>