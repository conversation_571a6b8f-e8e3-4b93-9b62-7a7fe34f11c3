package com.intensivereading.app.repository;

import android.content.Context;
import com.intensivereading.app.model.DayData;
import com.intensivereading.app.model.PageData;
import com.intensivereading.app.model.RecordingData;
import com.intensivereading.app.utils.FileUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据仓库类，管理应用的所有数据
 */
public class DataRepository {
    private static DataRepository instance;
    private Context context;
    private Map<String, DayData> dayDataMap;
    
    // 天的标识符数组
    private static final String[] DAY_IDS = {
        "monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"
    };

    // 天的显示名称数组
    private static final String[] DAY_NAMES = {
        "周一", "周二", "周三", "周四", "周五", "周六", "周日"
    };
    
    private DataRepository(Context context) {
        this.context = context.getApplicationContext();
        this.dayDataMap = new HashMap<>();
        initializeDayData();
    }
    
    public static synchronized DataRepository getInstance(Context context) {
        if (instance == null) {
            instance = new DataRepository(context);
        }
        return instance;
    }
    
    /**
     * 初始化天数据
     */
    private void initializeDayData() {
        for (int i = 0; i < DAY_IDS.length; i++) {
            DayData dayData = new DayData(DAY_IDS[i], DAY_NAMES[i]);
            dayDataMap.put(DAY_IDS[i], dayData);
            
            // 检查并初始化每一页的文件路径
            initializePagePaths(dayData);
        }
    }
    
    /**
     * 初始化页面文件路径
     */
    private void initializePagePaths(DayData dayData) {
        String dayId = dayData.getDayId();

        for (PageData page : dayData.getPages()) {
            // 设置图片路径
            String imagePath = FileUtils.getImagePath(context, dayId, page.getPageNumber());
            page.setImagePath(imagePath);

            // 检查是否存在音频文件，使用扫描功能确保文件有效
            String audioPath = FileUtils.getLatestAudioPath(context, dayId, page.getPageNumber());
            if (audioPath != null && FileUtils.isValidAudioFile(new File(audioPath))) {
                page.setAudioPath(audioPath);
                page.setRecorded(true);

                // 获取音频时长
                long duration = FileUtils.getAudioDuration(audioPath);
                page.setRecordingTime(duration);
            }
        }
    }
    
    /**
     * 获取所有天的数据
     */
    public List<DayData> getAllDayData() {
        List<DayData> dayDataList = new ArrayList<>();
        for (String dayId : DAY_IDS) {
            dayDataList.add(dayDataMap.get(dayId));
        }
        return dayDataList;
    }
    
    /**
     * 根据天ID获取天数据
     */
    public DayData getDayData(String dayId) {
        return dayDataMap.get(dayId);
    }
    
    /**
     * 更新页面的音频路径
     */
    public void updatePageAudioPath(String dayId, int pageNumber, String audioPath) {
        DayData dayData = dayDataMap.get(dayId);
        if (dayData != null) {
            PageData page = dayData.getPage(pageNumber);
            if (page != null) {
                page.setAudioPath(audioPath);
                page.setRecorded(true);
            }
        }
    }
    
    /**
     * 检查页面是否可以进入下一页
     */
    public boolean canProceedToNextPage(String dayId, int currentPage) {
        DayData dayData = dayDataMap.get(dayId);
        if (dayData != null) {
            PageData page = dayData.getPage(currentPage);
            return page != null && page.canProceedToNext();
        }
        return false;
    }
    
    /**
     * 获取天的索引
     */
    public int getDayIndex(String dayId) {
        for (int i = 0; i < DAY_IDS.length; i++) {
            if (DAY_IDS[i].equals(dayId)) {
                return i;
            }
        }
        return -1;
    }

    /**
     * 获取所有录音记录（只包含周一到周六）
     */
    public List<RecordingData> getAllRecordingData() {
        List<RecordingData> recordingList = new ArrayList<>();

        // 只遍历周一到周六（排除周日）
        for (int i = 0; i < DAY_IDS.length - 1; i++) {
            String dayId = DAY_IDS[i];
            String dayName = DAY_NAMES[i];
            DayData dayData = dayDataMap.get(dayId);

            if (dayData != null) {
                for (PageData page : dayData.getPages()) {
                    RecordingData recording = new RecordingData(dayId, dayName, page.getPageNumber());

                    if (page.hasAudio() && page.getAudioPath() != null) {
                        recording.setAudioPath(page.getAudioPath());
                        recording.setHasRecording(true);

                        // 获取录音时长
                        long duration = FileUtils.getAudioDuration(page.getAudioPath());
                        recording.setRecordingTime(duration);

                        // 获取录音开始时间
                        long startTime = FileUtils.extractTimestampFromAudioPath(page.getAudioPath());
                        recording.setStartTime(startTime);
                    }

                    recordingList.add(recording);
                }
            }
        }

        // 按录音时间倒序排列（最新的在前面）
        Collections.sort(recordingList, new Comparator<RecordingData>() {
            @Override
            public int compare(RecordingData r1, RecordingData r2) {
                // 如果都有录音，按录音时间排序
                if (r1.hasRecording() && r2.hasRecording()) {
                    return Long.compare(r2.getStartTime(), r1.getStartTime());
                }
                // 有录音的排在前面
                if (r1.hasRecording() && !r2.hasRecording()) {
                    return -1;
                }
                if (!r1.hasRecording() && r2.hasRecording()) {
                    return 1;
                }
                // 都没有录音，按日期和页面顺序排列
                int dayCompare = getDayIndex(r1.getDayId()) - getDayIndex(r2.getDayId());
                if (dayCompare != 0) {
                    return dayCompare;
                }
                return r1.getPageNumber() - r2.getPageNumber();
            }
        });

        return recordingList;
    }

    /**
     * 获取指定日期的录音记录
     */
    public List<RecordingData> getRecordingDataByDay(String dayId) {
        List<RecordingData> recordingList = new ArrayList<>();

        int dayIndex = getDayIndex(dayId);
        if (dayIndex == -1) {
            return recordingList;
        }

        String dayName = DAY_NAMES[dayIndex];
        DayData dayData = dayDataMap.get(dayId);

        if (dayData != null) {
            for (PageData page : dayData.getPages()) {
                RecordingData recording = new RecordingData(dayId, dayName, page.getPageNumber());

                if (page.hasAudio() && page.getAudioPath() != null) {
                    recording.setAudioPath(page.getAudioPath());
                    recording.setHasRecording(true);

                    // 获取录音时长
                    long duration = FileUtils.getAudioDuration(page.getAudioPath());
                    recording.setRecordingTime(duration);

                    // 获取录音开始时间
                    long startTime = FileUtils.extractTimestampFromAudioPath(page.getAudioPath());
                    recording.setStartTime(startTime);
                }

                recordingList.add(recording);
            }
        }

        return recordingList;
    }

    /**
     * 刷新音频数据（重新扫描所有音频文件）
     */
    public void refreshAudioData() {
        for (DayData dayData : dayDataMap.values()) {
            initializePagePaths(dayData);
        }
    }

    /**
     * 验证并清理无效的音频文件引用
     */
    public void validateAudioFiles() {
        for (DayData dayData : dayDataMap.values()) {
            for (PageData page : dayData.getPages()) {
                if (page.hasAudio() && page.getAudioPath() != null) {
                    File audioFile = new File(page.getAudioPath());
                    if (!FileUtils.isValidAudioFile(audioFile)) {
                        // 清除无效的音频文件引用
                        page.setAudioPath(null);
                        page.setRecorded(false);
                        page.setRecordingTime(0);
                    }
                }
            }
        }
    }
}
