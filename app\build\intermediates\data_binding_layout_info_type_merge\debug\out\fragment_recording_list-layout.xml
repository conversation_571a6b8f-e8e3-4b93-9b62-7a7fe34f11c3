<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_recording_list" modulePackage="com.intensivereading.app" filePath="app\src\main\res\layout\fragment_recording_list.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_recording_list_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="93" endOffset="51"/></Target><Target id="@+id/backButton" view="ImageButton"><Expressions/><location startLine="11" startOffset="4" endLine="22" endOffset="51"/></Target><Target id="@+id/titleText" view="TextView"><Expressions/><location startLine="25" startOffset="4" endLine="36" endOffset="51"/></Target><Target id="@+id/recordingRecyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="39" startOffset="4" endLine="51" endOffset="49"/></Target><Target id="@+id/emptyStateLayout" view="LinearLayout"><Expressions/><location startLine="54" startOffset="4" endLine="91" endOffset="18"/></Target></Targets></Layout>