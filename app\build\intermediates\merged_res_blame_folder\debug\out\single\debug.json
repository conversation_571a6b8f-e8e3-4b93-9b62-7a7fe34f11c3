[{"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-merged_res-44:\\drawable_ic_volume_up.xml.flat", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\drawable\\ic_volume_up.xml"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-merged_res-44:\\drawable_ic_play.xml.flat", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\drawable\\ic_play.xml"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-merged_res-44:\\anim_slide_in_left.xml.flat", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\anim\\slide_in_left.xml"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-merged_res-44:\\drawable_ic_launcher_background.xml.flat", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\drawable\\ic_launcher_background.xml"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-merged_res-44:\\drawable_ic_mic.xml.flat", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\drawable\\ic_mic.xml"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-merged_res-44:\\drawable_ic_launcher_foreground.xml.flat", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-merged_res-44:\\drawable_ic_history.xml.flat", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\drawable\\ic_history.xml"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-merged_res-44:\\drawable_rounded_background.xml.flat", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\drawable\\rounded_background.xml"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-merged_res-44:\\layout_item_recording.xml.flat", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\layout\\item_recording.xml"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-merged_res-44:\\layout-land_fragment_detail.xml.flat", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\layout-land\\fragment_detail.xml"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-merged_res-44:\\anim_slide_in_right.xml.flat", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\anim\\slide_in_right.xml"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-merged_res-44:\\drawable_circle_background.xml.flat", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\drawable\\circle_background.xml"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-merged_res-44:\\mipmap-hdpi_ic_launcher.png.flat", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\mipmap-hdpi\\ic_launcher.png"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-merged_res-44:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-merged_res-44:\\layout-land_fragment_main.xml.flat", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\layout-land\\fragment_main.xml"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-merged_res-44:\\drawable_ic_arrow_back.xml.flat", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\drawable\\ic_arrow_back.xml"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-merged_res-44:\\drawable_ic_navigate_next_selector.xml.flat", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\drawable\\ic_navigate_next_selector.xml"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-merged_res-44:\\drawable_ic_navigate_before.xml.flat", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\drawable\\ic_navigate_before.xml"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-merged_res-44:\\drawable_ic_navigate_before_selector.xml.flat", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\drawable\\ic_navigate_before_selector.xml"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-merged_res-44:\\navigation_nav_graph.xml.flat", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\navigation\\nav_graph.xml"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-merged_res-44:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-merged_res-44:\\layout_fragment_main.xml.flat", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\layout\\fragment_main.xml"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-merged_res-44:\\anim_slide_out_left.xml.flat", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\anim\\slide_out_left.xml"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-merged_res-44:\\drawable_ic_pause.xml.flat", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\drawable\\ic_pause.xml"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-merged_res-44:\\layout-land_fragment_recording_list.xml.flat", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\layout-land\\fragment_recording_list.xml"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-merged_res-44:\\drawable_ic_navigate_next.xml.flat", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\drawable\\ic_navigate_next.xml"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-merged_res-44:\\anim_slide_out_right.xml.flat", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\anim\\slide_out_right.xml"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-merged_res-44:\\xml_data_extraction_rules.xml.flat", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\xml\\data_extraction_rules.xml"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-merged_res-44:\\layout_fragment_detail.xml.flat", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\layout\\fragment_detail.xml"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-merged_res-44:\\xml_backup_rules.xml.flat", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\xml\\backup_rules.xml"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-merged_res-44:\\layout_fragment_recording_list.xml.flat", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\layout\\fragment_recording_list.xml"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-merged_res-44:\\layout_item_image_page.xml.flat", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\layout\\item_image_page.xml"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-merged_res-44:\\drawable_ic_calendar.xml.flat", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\drawable\\ic_calendar.xml"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-merged_res-44:\\layout_activity_main.xml.flat", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\layout\\activity_main.xml"}, {"merged": "com.intensivereading.app-merged_res-44:/layout_fragment_main.xml.flat", "source": "com.intensivereading.app-main-46:/layout/fragment_main.xml"}]