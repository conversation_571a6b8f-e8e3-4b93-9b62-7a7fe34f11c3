// Generated by view binder compiler. Do not edit!
package com.intensivereading.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.intensivereading.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemRecordingBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final TextView durationText;

  @NonNull
  public final ImageButton playButton;

  @NonNull
  public final TextView startTimeText;

  @NonNull
  public final TextView statusIcon;

  @NonNull
  public final TextView titleText;

  private ItemRecordingBinding(@NonNull CardView rootView, @NonNull TextView durationText,
      @NonNull ImageButton playButton, @NonNull TextView startTimeText,
      @NonNull TextView statusIcon, @NonNull TextView titleText) {
    this.rootView = rootView;
    this.durationText = durationText;
    this.playButton = playButton;
    this.startTimeText = startTimeText;
    this.statusIcon = statusIcon;
    this.titleText = titleText;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemRecordingBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemRecordingBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_recording, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemRecordingBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.durationText;
      TextView durationText = ViewBindings.findChildViewById(rootView, id);
      if (durationText == null) {
        break missingId;
      }

      id = R.id.playButton;
      ImageButton playButton = ViewBindings.findChildViewById(rootView, id);
      if (playButton == null) {
        break missingId;
      }

      id = R.id.startTimeText;
      TextView startTimeText = ViewBindings.findChildViewById(rootView, id);
      if (startTimeText == null) {
        break missingId;
      }

      id = R.id.statusIcon;
      TextView statusIcon = ViewBindings.findChildViewById(rootView, id);
      if (statusIcon == null) {
        break missingId;
      }

      id = R.id.titleText;
      TextView titleText = ViewBindings.findChildViewById(rootView, id);
      if (titleText == null) {
        break missingId;
      }

      return new ItemRecordingBinding((CardView) rootView, durationText, playButton, startTimeText,
          statusIcon, titleText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
