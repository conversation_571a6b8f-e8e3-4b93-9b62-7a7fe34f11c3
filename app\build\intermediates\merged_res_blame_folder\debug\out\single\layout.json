[{"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-mergeDebugResources-43:\\layout\\item_recording.xml", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\layout\\item_recording.xml"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-mergeDebugResources-43:\\layout\\fragment_recording_list.xml", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\layout\\fragment_recording_list.xml"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-mergeDebugResources-43:\\layout\\item_image_page.xml", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\layout\\item_image_page.xml"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-mergeDebugResources-43:\\layout\\fragment_detail.xml", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\layout\\fragment_detail.xml"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-mergeDebugResources-43:\\layout\\activity_main.xml", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\layout\\activity_main.xml"}, {"merged": "com.intensivereading.app-mergeDebugResources-43:/layout/fragment_main.xml", "source": "com.intensivereading.app-main-46:/layout/fragment_main.xml"}, {"merged": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-mergeDebugResources-43:\\layout\\fragment_main.xml", "source": "E:\\vscode_cache_jason\\daemon\\8.0\\com.intensivereading.app-main-46:\\layout\\fragment_main.xml"}]