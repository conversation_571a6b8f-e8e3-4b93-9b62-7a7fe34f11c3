// Generated by view binder compiler. Do not edit!
package com.intensivereading.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.GridLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.intensivereading.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentMainBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ConstraintLayout contentLayout;

  @NonNull
  public final GridLayout daysGrid;

  @NonNull
  public final MaterialButton fridayButton;

  @NonNull
  public final MaterialButton mondayButton;

  @NonNull
  public final MaterialButton recordingListButton;

  @NonNull
  public final MaterialButton saturdayButton;

  @NonNull
  public final MaterialButton thursdayButton;

  @NonNull
  public final MaterialButton tuesdayButton;

  @NonNull
  public final MaterialButton wednesdayButton;

  private FragmentMainBinding(@NonNull ConstraintLayout rootView,
      @NonNull ConstraintLayout contentLayout, @NonNull GridLayout daysGrid,
      @NonNull MaterialButton fridayButton, @NonNull MaterialButton mondayButton,
      @NonNull MaterialButton recordingListButton, @NonNull MaterialButton saturdayButton,
      @NonNull MaterialButton thursdayButton, @NonNull MaterialButton tuesdayButton,
      @NonNull MaterialButton wednesdayButton) {
    this.rootView = rootView;
    this.contentLayout = contentLayout;
    this.daysGrid = daysGrid;
    this.fridayButton = fridayButton;
    this.mondayButton = mondayButton;
    this.recordingListButton = recordingListButton;
    this.saturdayButton = saturdayButton;
    this.thursdayButton = thursdayButton;
    this.tuesdayButton = tuesdayButton;
    this.wednesdayButton = wednesdayButton;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.contentLayout;
      ConstraintLayout contentLayout = ViewBindings.findChildViewById(rootView, id);
      if (contentLayout == null) {
        break missingId;
      }

      id = R.id.daysGrid;
      GridLayout daysGrid = ViewBindings.findChildViewById(rootView, id);
      if (daysGrid == null) {
        break missingId;
      }

      id = R.id.fridayButton;
      MaterialButton fridayButton = ViewBindings.findChildViewById(rootView, id);
      if (fridayButton == null) {
        break missingId;
      }

      id = R.id.mondayButton;
      MaterialButton mondayButton = ViewBindings.findChildViewById(rootView, id);
      if (mondayButton == null) {
        break missingId;
      }

      id = R.id.recordingListButton;
      MaterialButton recordingListButton = ViewBindings.findChildViewById(rootView, id);
      if (recordingListButton == null) {
        break missingId;
      }

      id = R.id.saturdayButton;
      MaterialButton saturdayButton = ViewBindings.findChildViewById(rootView, id);
      if (saturdayButton == null) {
        break missingId;
      }

      id = R.id.thursdayButton;
      MaterialButton thursdayButton = ViewBindings.findChildViewById(rootView, id);
      if (thursdayButton == null) {
        break missingId;
      }

      id = R.id.tuesdayButton;
      MaterialButton tuesdayButton = ViewBindings.findChildViewById(rootView, id);
      if (tuesdayButton == null) {
        break missingId;
      }

      id = R.id.wednesdayButton;
      MaterialButton wednesdayButton = ViewBindings.findChildViewById(rootView, id);
      if (wednesdayButton == null) {
        break missingId;
      }

      return new FragmentMainBinding((ConstraintLayout) rootView, contentLayout, daysGrid,
          fridayButton, mondayButton, recordingListButton, saturdayButton, thursdayButton,
          tuesdayButton, wednesdayButton);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
