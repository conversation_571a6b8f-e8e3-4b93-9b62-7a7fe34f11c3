<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\res"><file name="slide_in_left" path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\res\anim\slide_in_left.xml" qualifiers="" type="anim"/><file name="slide_in_right" path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\res\anim\slide_in_right.xml" qualifiers="" type="anim"/><file name="slide_out_left" path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\res\anim\slide_out_left.xml" qualifiers="" type="anim"/><file name="slide_out_right" path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\res\anim\slide_out_right.xml" qualifiers="" type="anim"/><file name="circle_background" path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\res\drawable\circle_background.xml" qualifiers="" type="drawable"/><file name="ic_arrow_back" path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\res\drawable\ic_arrow_back.xml" qualifiers="" type="drawable"/><file name="ic_calendar" path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\res\drawable\ic_calendar.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_mic" path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\res\drawable\ic_mic.xml" qualifiers="" type="drawable"/><file name="ic_navigate_before" path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\res\drawable\ic_navigate_before.xml" qualifiers="" type="drawable"/><file name="ic_navigate_before_selector" path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\res\drawable\ic_navigate_before_selector.xml" qualifiers="" type="drawable"/><file name="ic_navigate_next" path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\res\drawable\ic_navigate_next.xml" qualifiers="" type="drawable"/><file name="ic_navigate_next_selector" path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\res\drawable\ic_navigate_next_selector.xml" qualifiers="" type="drawable"/><file name="ic_pause" path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\res\drawable\ic_pause.xml" qualifiers="" type="drawable"/><file name="ic_play" path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\res\drawable\ic_play.xml" qualifiers="" type="drawable"/><file name="rounded_background" path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\res\drawable\rounded_background.xml" qualifiers="" type="drawable"/><file name="activity_main" path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="fragment_detail" path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\res\layout\fragment_detail.xml" qualifiers="" type="layout"/><file name="fragment_main" path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\res\layout\fragment_main.xml" qualifiers="" type="layout"/><file name="fragment_recording_list" path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\res\layout\fragment_recording_list.xml" qualifiers="" type="layout"/><file name="item_image_page" path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\res\layout\item_image_page.xml" qualifiers="" type="layout"/><file name="item_recording" path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\res\layout\item_recording.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="nav_graph" path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\res\navigation\nav_graph.xml" qualifiers="" type="navigation"/><file path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="primary_color">#FF2196F3</color><color name="primary_dark_color">#FF1976D2</color><color name="accent_color">#FFFF9800</color><color name="button_enabled">#FF4CAF50</color><color name="button_disabled">#FFBDBDBD</color><color name="button_recording">#FFFF5722</color><color name="button_playing">#FF2196F3</color><color name="success_color">#FF4CAF50</color><color name="icon_enabled">#FFFFFFFF</color><color name="icon_disabled">#80FFFFFF</color><color name="background_light">#FFF5F5F5</color><color name="background_dark">#FF303030</color><color name="text_primary">#FF212121</color><color name="text_secondary">#FF757575</color><color name="text_hint">#FFBDBDBD</color></file><file path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="control_bar_height">80dp</dimen><dimen name="control_bar_height_landscape">56dp</dimen><dimen name="control_button_size">48dp</dimen><dimen name="control_button_size_landscape">40dp</dimen><dimen name="control_bar_padding">12dp</dimen><dimen name="control_bar_padding_landscape">6dp</dimen><dimen name="control_bar_padding_horizontal">16dp</dimen><dimen name="recording_card_margin">32dp</dimen><dimen name="recording_card_margin_landscape">24dp</dimen><dimen name="recording_card_padding">32dp</dimen><dimen name="recording_card_padding_landscape">24dp</dimen><dimen name="recording_icon_size">64dp</dimen><dimen name="recording_icon_size_landscape">48dp</dimen></file><file path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">六天精读</string><string name="monday">周一</string><string name="tuesday">周二</string><string name="wednesday">周三</string><string name="thursday">周四</string><string name="friday">周五</string><string name="saturday">周六</string><string name="page_indicator">%1$d/%2$d</string><string name="previous">上一个</string><string name="next">下一个</string><string name="play">播放</string><string name="pause">暂停</string><string name="record">录音</string><string name="recording">录音中...</string><string name="back">返回</string><string name="permission_audio_title">需要录音权限</string><string name="permission_audio_message">应用需要录音权限来录制音频内容</string><string name="permission_storage_title">需要存储权限</string><string name="permission_storage_message">应用需要存储权限来保存音频文件</string><string name="permission_denied">权限被拒绝</string><string name="grant_permission">授予权限</string><string name="cancel">取消</string><string name="error_recording_failed">录音失败</string><string name="error_playback_failed">播放失败</string><string name="error_file_not_found">文件未找到</string><string name="error_permission_required">需要相关权限才能使用此功能</string><string name="recording_too_short">录音时间太短，至少需要2秒</string><string name="long_press_to_record">长按录音</string><string name="recording_saved">录音已保存</string><string name="no_audio_file">没有音频文件</string><string name="sunday">周日</string><string name="playback_completed">播放完成</string><string name="no_recordings">暂无录音记录</string><string name="play_recording">播放录音</string><string name="pause_recording">暂停录音</string><string name="loading_recordings">加载录音数据失败: %s</string><string name="playing_title">播放: %s</string><string name="playback_paused">播放暂停</string><string name="duration_format">时长: %s</string><string name="recording_list">学习记录</string><string name="playback_error">播放错误: %s</string><string name="no_recordings_hint">开始学习并录音后，记录将显示在这里</string></file><file path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.IntensiveReading" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryVariant">@color/primary_dark_color</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/accent_color</item>
        <item name="colorSecondaryVariant">@color/accent_color</item>
        <item name="colorOnSecondary">@color/white</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style><style name="MainButtonStyle" parent="Widget.Material3.Button">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">120dp</item>
        <item name="android:layout_margin">8dp</item>
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
        <item name="cornerRadius">12dp</item>
    </style><style name="MainButtonStyleLandscape" parent="Widget.Material3.Button">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">80dp</item>
        <item name="android:layout_margin">6dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="cornerRadius">12dp</item>
    </style><style name="ControlButtonStyle" parent="Widget.Material3.Button.Icon">
        <item name="android:layout_width">64dp</item>
        <item name="android:layout_height">64dp</item>
        <item name="android:layout_margin">8dp</item>
        <item name="cornerRadius">32dp</item>
    </style><style name="RecordButtonStyle" parent="ControlButtonStyle">
        <item name="android:backgroundTint">@color/button_recording</item>
    </style><style name="PlayButtonStyle" parent="ControlButtonStyle">
        <item name="android:backgroundTint">@color/button_playing</item>
    </style></file><file path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\res\values-land\dimens.xml" qualifiers="land"><dimen name="control_bar_height">56dp</dimen><dimen name="control_button_size">40dp</dimen><dimen name="control_bar_padding">6dp</dimen><dimen name="control_bar_padding_horizontal">16dp</dimen><dimen name="recording_card_margin">24dp</dimen><dimen name="recording_card_padding">24dp</dimen><dimen name="recording_icon_size">48dp</dimen></file><file name="backup_rules" path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="ic_history" path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\res\drawable\ic_history.xml" qualifiers="" type="drawable"/><file name="ic_volume_up" path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\res\drawable\ic_volume_up.xml" qualifiers="" type="drawable"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\renzheng\src\6_steps_Intensive_Reading\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\renzheng\src\6_steps_Intensive_Reading\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\renzheng\src\6_steps_Intensive_Reading\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>