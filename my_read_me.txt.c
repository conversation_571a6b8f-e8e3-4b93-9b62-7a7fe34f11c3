
帮忙写一个  Augment 规则配置文件 ,包含下面的要求 
- 使用Java 8+语法特性，包括Lambda表达式和方法引用
- 使用现代Java特性如Optional、Stream API等（在适当的地方）
- 保持ViewBinding的使用方式
- 保持MVVM架构模式
- 不使用 kotlin语言. 
- 基于 Android 12 及其以上的设备,不需要考虑低版本设备的版本兼容性

横屏显示 

支持下面2种设备硬件信息如下:
基于android 12 手持设备,
设备1:
 Physical size: 720x1440  ;
Physical density: 320

设备2:
127|xun:/ $  wm size
Physical size: 1200x1920
xun:/ $ wm density
Physical density: 280



 

有8个按键,名字分别表示周一到周日
每一个按键按下 弹出一个 主页面
每一个 主页面 中 有个多个  ViewPager2  
每个  ViewPager2 的底部有4个按键,  名字分别是 :上一个,播放 ,录音,下一个
主页面 中 有个多个  ViewPager2   ,通过 上一个,下一个 这2个按键,进行切换.

录音 按键 按下后 进行录音,松开后 暂停录音 ,
播放 按键 按下后 播放指定的音频文件.

页面切换机制 推荐 一种 快的 现代化的 ,不需要考虑兼容旧设备的 方式 .
录音 按键 按下后进行录音

 
 
 
 

- 横屏显示
127|rothko:/ $  wm size
Physical size: 1220x2712
rothko:/ $  wm density
Physical density: 520
rothko:/ $



我想开发一个 Android 应用，具体要求如下：

 
**应用功能需求：**

1. **主界面布局：**
   - 8个按钮，分别标记为"周一"到"周6"和一个额外按钮
   - 每个按钮点击后跳转到对应的详情页面

2. **详情页面结构：**
   - 每个详情页面包含多个 ViewPager2 组件
   - 每个 ViewPager2 底部有4个控制按钮：
     - "上一个"：在最底部,切换到前一个 ViewPager2  
     - "播放"：在最底部,播放当前关联的音频文件
     - "录音"：在最底部,按住录音，松开停止
     - "下一个"：在最底部,切换到后一个 ViewPager2
	  - "返回"：在界面的左上角 ,按下后 ,退出 详情页面 回到 主界面
	  

3. **音频功能：**
   - 录音功能：按住"录音"按钮开始录音，松开按钮停止录音
   - 播放功能：点击"播放"按钮播放/ app所在的data目录的 files 目录下的 指定的音频文件
   - 需要处理音频文件的存储和管理

4. **技术要求：**
   - 使用现代化的页面切换机制（推荐 Navigation Component + Fragment）
   - 不需要考虑 Android 12 以下版本的兼容性
   - 优先考虑性能和用户体验
	- 不使用 Kotlin

**需要实现的具体功能：**
- 页面导航和切换动画
- 音频录制和播放
- ViewPager2 的动态切换


请提供架构建议、关键代码实现和最佳实践。

 
 
 
  
 
 {
 我需要开发一个Android应用程序，用于六天学习内容的管理和音频记录。以下是详细的功能和技术规范：

**核心功能规范：**

1. **主界面设计：**
   - 使用GridLayout创建1x6网格布局，包含6个按钮
   - 按钮标签：["周一", "周二", "周三", "周四", "周五", "周六"]
   - 每个按钮点击后通过Navigation Component导航到对应的详情Fragment
   - 按钮样式：Material Design风格，统一尺寸和间距

2. **详情页面架构：**
   - **页面结构：** 每个详情页面默认包含10个ViewPager2页面
   - **图片展示：** 每个ViewPager2页面全屏显示一张图片
   - **存储路径：** 图片存储在`context.getFilesDir()/[页面标识]/images/`目录
   - **音频存储：** 录音文件存储在`context.getFilesDir()/[页面标识]/audio/`目录

3. **UI控件布局：**
   - **顶部导航栏：**
     - 左上角：返回按钮（Material Icons: arrow_back）
     - 右上角：页面指示器（格式："当前页/总页数"，如"3/10"）
   - **底部控制栏（固定位置）：**
     - "上一个"按钮：切换到前一个ViewPager2页面，第一页时禁用
     - "播放"按钮：播放当前页面关联的音频文件，动态切换图标
     - "录音"按钮：长按录音功能，录音成功后图标变化并启用"下一个"按钮
     - "下一个"按钮：默认禁用，录音完成后启用，切换到下一个ViewPager2页面

4. **音频功能详细规范：**
   - **录音实现：**
     - API：使用MediaRecorder
     - 格式：AAC编码，44.1kHz采样率
     - 最短录音时长：2秒（低于2秒不保存）
     - 文件命名：`audio_[页面标识]_[页面序号]_[时间戳].aac`
     - 视觉反馈：录音时显示波形动画和计时器
   - **播放实现：**
     - API：使用MediaPlayer
     - 功能：播放/暂停切换，进度条显示
     - 行为：切换ViewPager2页面时自动暂停播放
     - 状态管理：播放时显示"暂停"图标，暂停时显示"播放"图标

5. **技术架构要求：**
   - **开发语言：** Java（严格不使用Kotlin）
   - **最低API：** Android API 31 (targetSdkVersion 34)
   - **架构模式：** MVVM + Repository Pattern
   - **导航：** Navigation Component + Single Activity + Multiple Fragments
   - **依赖注入：** 可选择Dagger2或手动依赖管理
   - **构建工具：** Gradle 8.0+

6. **包结构设计：**
```
com.yourpackage.intensivereading/
├── ui/
│   ├── main/           # 主界面相关
│   ├── detail/         # 详情页面相关
│   └── common/         # 通用UI组件
├── audio/
│   ├── recorder/       # 录音功能
│   ├── player/         # 播放功能
│   └── manager/        # 音频管理
├── navigation/         # 导航相关
├── repository/         # 数据仓库
├── utils/              # 工具类
└── model/              # 数据模型
```

7. **权限和生命周期管理：**
   - **必需权限：** RECORD_AUDIO, READ_EXTERNAL_STORAGE, WRITE_EXTERNAL_STORAGE
   - **权限处理：** 使用ActivityResultContracts处理运行时权限
   - **音频焦点：** 实现AudioFocusRequest管理
   - **生命周期：** 正确处理Fragment生命周期，防止内存泄漏

8. **性能和用户体验要求：**
   - **动画性能：** ViewPager2切换动画保持60fps
   - **音频延迟：** 播放启动延迟<100ms
   - **内存管理：** 图片使用Glide加载，实现内存缓存
   - **错误处理：** 完整的异常捕获和用户友好的错误提示
   - **状态保存：** 处理设备旋转和应用后台恢复

**交付物清单：**
1. **项目结构：** 完整的Android Studio项目
2. **核心代码：** MainActivity, DetailFragment, AudioManager, NavigationGraph
3. **布局文件：** 所有XML布局文件
4. **配置文件：** AndroidManifest.xml, build.gradle配置
5. **文档：** 代码注释和README使用说明

**验收标准：**
- 所有6个页面可以正常导航
- 录音功能正常工作（长按录音，短按无效）
- 播放功能正常（播放完成结束播放，页面切换时暂停）
- 页面指示器正确显示当前位置
- 应用在Android 12+设备上稳定运行
- 内存使用合理，无明显内存泄漏
 }
 
 
 
 {
 
 
 10个照片
 增加一个播放按键 ,可以播放录音,
 如果已经录音了,那么 录音按键 形状不一样 ,
 
 增加一个按键 ,按下后 ,显示 学习记录详情页,周一到周日 的  录音情况,是否录音 ,录音开始时间 ,录音时长,  
 在 学习记录详情页, 为每一个录音添加一个按键 ,按下后 播放录音 .
 
 添加学习内容: 将实际的图片文件放入对应目录
运行测试: 使用提供的构建检查清单进行完整测试
性能优化: 根据实际使用情况进行进一步优化
功能扩展: 可以考虑添加学习进度统计、云同步等功能
 }
 
 {
 
 请根据 硬件信息 优化下界面 
 横屏 下 按键显示不全
 ViewPager2 不需要标题 : 六天精读  ,需要删除标题. 
 每个ViewPager2页面全屏显示一张图片 
 
 独步4个按键 不需要背景 
 }
 
 {
 
 
app 运行后 强制 横屏显示 不能 竖屏新海狮

支持下面2种设备硬件信息如下:
基于android 12 手持设备,
设备1:
Physical size: 720x1440  ;
Physical density: 320

设备2:
127|xun:/ $  wm size
Physical size: 1200x1920
xun:/ $ wm density
Physical density: 280

根据  设备硬件信息 优化 主界面设计 的按键 ,现在显示屏不全
每一个 ViewPager2 的 标题 隐藏  
每一个 ViewPager2 的 按键不需要背景 
每一个 ViewPager2 的 需要全屏显示 图片




 }
 
{

录音时间 少于 1秒 不需要 弹出提示 ,
长按录音功能 时间大于1秒 ,才会保存录音文件, 录音成功后图标变化并启用"下一个"按钮


全屏显示  状态栏 隐藏 ,
删除 主页面最顶部的 六天精读 
删除 ViewPager2 最顶部的 六天精读 
每个ViewPager2 底部的4个按键  放在中间底部 ,记在中间,间隔5dp

- 在详情页面左上角添加"返回"按钮，点击后返回主界面
- 显示在右上角 当前页面位置指示器（如 "1/7"）

 ViewPager2 显示后 ,
}

{
1修正bug: 在详情页 的第一个 ViewPager2 中 没有录音也可以 切换到 第二个 ViewPager2 
2修改: 删除 主页面最顶部的 六天精读  这个标题,不需要
删除 ViewPager2 最顶部的 六天精读 这个标题 ,不需要

3增加功能: 主界面 增加一个按键 ,按下后 ,显示 学习记录详情页,周一到周日 的  录音情况,是否录音 ,录音开始时间 ,录音时长,  
 在 学习记录详情页, 为每一个录音添加一个按键 ,按下后 播放录音 .
 

1修正bug: 在详情页 的第一个 ViewPager2 中 没有录音也可以 切换到 第二个 ViewPager2 
2修改: 删除 主页面最顶部的 六天精读  这个标题,不需要
删除 ViewPager2 最顶部的 六天精读 这个标题 ,不需要

3增加功能: 主界面 没看到 有 增加一个按键  ,用来 显示 学习记录详情页 
 

}