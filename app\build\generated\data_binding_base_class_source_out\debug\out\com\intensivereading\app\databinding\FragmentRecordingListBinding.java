// Generated by view binder compiler. Do not edit!
package com.intensivereading.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.intensivereading.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentRecordingListBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ImageButton backButton;

  @NonNull
  public final LinearLayout emptyStateLayout;

  @NonNull
  public final RecyclerView recordingRecyclerView;

  @NonNull
  public final TextView titleText;

  private FragmentRecordingListBinding(@NonNull ConstraintLayout rootView,
      @NonNull ImageButton backButton, @NonNull LinearLayout emptyStateLayout,
      @NonNull RecyclerView recordingRecyclerView, @NonNull TextView titleText) {
    this.rootView = rootView;
    this.backButton = backButton;
    this.emptyStateLayout = emptyStateLayout;
    this.recordingRecyclerView = recordingRecyclerView;
    this.titleText = titleText;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentRecordingListBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentRecordingListBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_recording_list, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentRecordingListBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.backButton;
      ImageButton backButton = ViewBindings.findChildViewById(rootView, id);
      if (backButton == null) {
        break missingId;
      }

      id = R.id.emptyStateLayout;
      LinearLayout emptyStateLayout = ViewBindings.findChildViewById(rootView, id);
      if (emptyStateLayout == null) {
        break missingId;
      }

      id = R.id.recordingRecyclerView;
      RecyclerView recordingRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (recordingRecyclerView == null) {
        break missingId;
      }

      id = R.id.titleText;
      TextView titleText = ViewBindings.findChildViewById(rootView, id);
      if (titleText == null) {
        break missingId;
      }

      return new FragmentRecordingListBinding((ConstraintLayout) rootView, backButton,
          emptyStateLayout, recordingRecyclerView, titleText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
