<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    tools:context=".ui.main.MainFragment">

    <!-- 主要内容区域 - 横屏优化 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/contentLayout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 7个按钮的网格布局 - 横屏4x2布局 -->
        <GridLayout
            android:id="@+id/daysGrid"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:columnCount="4"
            android:rowCount="2"
            android:alignmentMode="alignBounds"
            android:columnOrderPreserved="false"
            android:useDefaultMargins="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">

            <!-- 周一按钮 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/mondayButton"
                style="@style/MainButtonStyleLandscape"
                android:layout_columnWeight="1"
                android:text="@string/monday"
                android:backgroundTint="@color/primary_color"
                app:icon="@drawable/ic_calendar"
                app:iconGravity="top"
                app:iconSize="24dp" />

            <!-- 周二按钮 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/tuesdayButton"
                style="@style/MainButtonStyleLandscape"
                android:layout_columnWeight="1"
                android:text="@string/tuesday"
                android:backgroundTint="@color/primary_color"
                app:icon="@drawable/ic_calendar"
                app:iconGravity="top"
                app:iconSize="24dp" />

            <!-- 周三按钮 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/wednesdayButton"
                style="@style/MainButtonStyleLandscape"
                android:layout_columnWeight="1"
                android:text="@string/wednesday"
                android:backgroundTint="@color/primary_color"
                app:icon="@drawable/ic_calendar"
                app:iconGravity="top"
                app:iconSize="24dp" />

            <!-- 周四按钮 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/thursdayButton"
                style="@style/MainButtonStyleLandscape"
                android:layout_columnWeight="1"
                android:text="@string/thursday"
                android:backgroundTint="@color/primary_color"
                app:icon="@drawable/ic_calendar"
                app:iconGravity="top"
                app:iconSize="24dp" />

            <!-- 周五按钮 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/fridayButton"
                style="@style/MainButtonStyleLandscape"
                android:layout_columnWeight="1"
                android:text="@string/friday"
                android:backgroundTint="@color/primary_color"
                app:icon="@drawable/ic_calendar"
                app:iconGravity="top"
                app:iconSize="24dp" />

            <!-- 周六按钮 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/saturdayButton"
                style="@style/MainButtonStyleLandscape"
                android:layout_columnWeight="1"
                android:text="@string/saturday"
                android:backgroundTint="@color/primary_color"
                app:icon="@drawable/ic_calendar"
                app:iconGravity="top"
                app:iconSize="24dp" />

        </GridLayout>

        <!-- 学习记录按钮 - 横屏布局 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/recordingListButton"
            style="@style/Widget.Material3.Button.Icon"
            android:layout_width="wrap_content"
            android:layout_height="48dp"
            android:layout_marginTop="16dp"
            android:text="@string/recording_list"
            android:textSize="14sp"
            android:textStyle="bold"
            android:backgroundTint="@android:color/transparent"
            android:textColor="@color/primary_color"
            app:icon="@drawable/ic_history"
            app:iconGravity="start"
            app:iconSize="20dp"
            app:iconTint="@color/primary_color"
            app:strokeWidth="0dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/daysGrid" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
