<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    tools:context=".ui.detail.DetailFragment">

    <!-- 左上角返回按钮 -->
    <ImageButton
        android:id="@+id/backButton"
        android:layout_width="44dp"
        android:layout_height="44dp"
        android:layout_margin="12dp"
        android:background="@drawable/circle_background"
        android:contentDescription="@string/back"
        android:src="@drawable/ic_arrow_back"
        android:scaleType="centerInside"
        android:elevation="8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 右上角页面指示器 -->
    <TextView
        android:id="@+id/pageIndicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="12dp"
        android:background="@drawable/rounded_background"
        android:padding="6dp"
        android:text="1/10"
        android:textColor="@color/white"
        android:textSize="12sp"
        android:textStyle="bold"
        android:elevation="8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- ViewPager2 用于显示图片 - 全屏显示 -->
    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPager"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/bottomControlBar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 底部控制栏 - 横屏水平居中对齐 -->
    <LinearLayout
        android:id="@+id/bottomControlBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="@drawable/rounded_background"
        android:padding="8dp"
        android:layout_marginBottom="12dp"
        android:elevation="8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <!-- 上一个按钮 -->
        <ImageButton
            android:id="@+id/previousButton"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginEnd="5dp"
            android:background="@android:color/transparent"
            android:contentDescription="@string/previous"
            android:enabled="false"
            android:src="@drawable/ic_navigate_before_selector"
            android:scaleType="centerInside" />

        <!-- 播放按钮 -->
        <ImageButton
            android:id="@+id/playButton"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginEnd="5dp"
            android:background="@android:color/transparent"
            android:contentDescription="@string/play"
            android:src="@drawable/ic_play"
            android:scaleType="centerInside" />

        <!-- 录音按钮 -->
        <ImageButton
            android:id="@+id/recordButton"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginEnd="5dp"
            android:background="@android:color/transparent"
            android:contentDescription="@string/record"
            android:src="@drawable/ic_mic"
            android:scaleType="centerInside" />

        <!-- 下一个按钮 -->
        <ImageButton
            android:id="@+id/nextButton"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@android:color/transparent"
            android:contentDescription="@string/next"
            android:enabled="false"
            android:src="@drawable/ic_navigate_next_selector"
            android:scaleType="centerInside" />

    </LinearLayout>

    <!-- 录音状态覆盖层 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/recordingOverlay"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#80000000"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/bottomControlBar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 录音状态卡片 - 横屏优化 -->
        <androidx.cardview.widget.CardView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="24dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="8dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal"
                android:padding="24dp">

                <!-- 录音图标 -->
                <ImageView
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:layout_marginEnd="16dp"
                    android:src="@drawable/ic_mic"
                    android:tint="@color/button_recording" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!-- 录音状态文本 -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="4dp"
                        android:text="@string/recording"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <!-- 录音时长 -->
                    <TextView
                        android:id="@+id/recordingTimeText"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="00:00"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
