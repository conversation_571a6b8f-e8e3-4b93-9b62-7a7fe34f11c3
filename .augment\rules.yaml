# Augment 规则配置文件 - 六天精读 Android 应用
version: "1.0"

# 项目基本信息
project:
  name: "IntensiveReading"
  type: "android"
  language: "java"
  min_api: 31
  target_api: 34

# 代码生成规则
code_generation:
  # Java 语言规范
  java:
    version: "8+"
    features:
      - lambda_expressions
      - method_references
      - optional_api
      - stream_api
      - default_methods
      - functional_interfaces
    
    # 禁用 Kotlin
    forbidden_languages:
      - kotlin
    
    # 代码风格
    style:
      naming_convention: "camelCase"
      max_line_length: 120
      indent_size: 4
      use_spaces: true

  # Android 特定规则
  android:
    # 架构模式
    architecture: "MVVM"
    
    # UI 绑定
    view_binding: true
    data_binding: false
    
    # 组件使用
    components:
      - "Navigation Component"
      - "ViewPager2"
      - "ConstraintLayout"
      - "Material Design 3"
    
    # 权限管理
    permissions:
      runtime_permissions: true
      target_api_31_plus: true

# 设备适配规则
device_support:
  # 强制横屏显示规则
  orientation:
    forced_landscape: true
    primary: "landscape"
    secondary: null  # 不支持竖屏
    lock_orientation: true
    
    # AndroidManifest.xml 配置要求
    manifest_requirements:
      - "android:screenOrientation=\"landscape\""
      - "android:configChanges=\"orientation|screenSize|keyboardHidden\""
    
    # 代码层面强制横屏
    code_enforcement:
      - "在MainActivity.onCreate()中调用setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE)"
      - "禁用屏幕旋转监听器"
      - "移除orientation change处理逻辑"

  # 目标设备（仅横屏模式）
  target_devices:
    - type: "phone"
      screen_size: "normal"
      density: "xhdpi"  # 320dpi
      resolution: "720x1440"
      landscape_resolution: "1440x720"  # 横屏实际分辨率
      orientation: ["landscape"]  # 仅支持横屏
    
    - type: "phone"
      screen_size: "large" 
      density: "hdpi"   # 280dpi
      resolution: "1200x1920"
      landscape_resolution: "1920x1200"  # 横屏实际分辨率
      orientation: ["landscape"]  # 仅支持横屏

  # 横屏优化规则
  landscape_optimizations:
    - "所有UI设计基于横屏模式"
    - "按钮布局采用横向排列"
    - "控件尺寸针对横屏操作优化"
    - "确保所有UI元素在横屏下完全可见"
    - "利用横屏宽度优势优化用户体验"

# 布局规则
layout_rules:
  # 强制横屏布局开发
  orientation_policy:
    landscape_only: true
    simplified_development: true
    
    # 布局目录规则
    layout_directories:
      primary: "layout/"  # 统一使用默认布局目录
      forbidden:
        - "layout-land/"     # 禁用横屏专用目录
        - "layout-port/"     # 禁用竖屏专用目录
        - "layout-sw600dp-port/"  # 禁用竖屏平板目录
      
    # 开发简化规则
    development_rules:
      - "所有布局文件统一放在res/layout/目录"
      - "不需要创建orientation变体布局"
      - "不需要处理屏幕旋转逻辑"
      - "UI设计只考虑横屏显示效果"

  # 响应式设计（横屏专用）
  responsive_design:
    use_constraint_layout: true
    landscape_focused: true
    
    # 尺寸适配（横屏优化）
    dimension_units:
      preferred: "dp"
      text_size: "sp"
      avoid: "px"
      
    # 横屏专用尺寸定义
    landscape_dimensions:
      control_bar_height: "56dp"
      button_size: "40dp"
      padding_horizontal: "16dp"
      padding_vertical: "8dp"

  # 控件规范（横屏优化）
  ui_components:
    buttons:
      style: "Material Design 3"
      min_touch_target: "40dp"  # 横屏优化尺寸
      layout_direction: "horizontal"
      spacing: "8dp"
    
    images:
      loading_library: "Glide"
      scale_type: "centerCrop"
      placeholder: true
      aspect_ratio: "landscape_optimized"
    
    text:
      font_scaling: true
      accessibility: true
      line_height_optimization: true

  # ViewPager2 横屏优化
  viewpager_rules:
    orientation: "horizontal"
    full_screen_images: true
    hide_titles: true
    bottom_controls_centered: true
    control_spacing: "5dp"

# 功能实现规则
feature_rules:
  # 音频处理
  audio:
    recorder: "MediaRecorder"
    player: "MediaPlayer" 
    format: "AAC"
    quality: "high"
    
    # 现代Java特性使用
    implementation_style:
      - "使用Optional处理空值"
      - "使用Stream API处理集合"
      - "使用Lambda表达式简化回调"
      - "使用方法引用优化代码"

  # 文件管理
  file_management:
    storage_type: "internal"
    use_scoped_storage: true
    file_operations:
      - "使用Files.walk()遍历目录"
      - "使用Stream API过滤文件"
      - "使用Optional处理文件不存在情况"

  # 导航
  navigation:
    component: "Navigation Component"
    safe_args: true
    deep_links: false

# 性能优化规则
performance:
  # 内存管理
  memory:
    use_weak_references: true
    lifecycle_aware: true
    avoid_memory_leaks: true
  
  # UI性能
  ui_performance:
    recyclerview_optimization: true
    image_caching: true
    smooth_animations: true
    target_fps: 60

# 代码质量规则
code_quality:
  # 错误处理
  error_handling:
    use_try_with_resources: true
    use_optional_for_nulls: true
    proper_exception_handling: true
  
  # 日志规范
  logging:
    use_android_log: true
    log_levels: ["DEBUG", "INFO", "WARN", "ERROR"]
    tag_format: "ClassName"

# 测试规则
testing:
  unit_tests: true
  instrumentation_tests: true
  test_coverage_target: 80

# 构建规则
build:
  gradle_version: "8.0+"
  build_tools: "34.0.0"
  
  # 依赖管理
  dependencies:
    androidx_libraries: true
    material_design: "1.10.0+"
    glide: "4.15.0+"
    
  # 构建优化
  optimizations:
    minify_enabled: false  # Debug模式
    proguard_enabled: true # Release模式
    
# 特殊指令
special_instructions:
  - "所有新代码必须使用Java 8+语法特性"
  - "优先使用Stream API处理集合操作"
  - "使用Optional避免空指针异常"
  - "Lambda表达式优先于匿名内部类"
  - "方法引用优先于Lambda表达式（当适用时）"
  - "确保横屏模式下UI完全可用"
  - "所有布局必须在两种目标设备上测试"
  - "保持MVVM架构的清晰分离"
  - "ViewBinding必须在所有Fragment和Activity中使用"
