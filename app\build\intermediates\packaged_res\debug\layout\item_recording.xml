<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:cardBackgroundColor="@color/white">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- 录音状态图标 -->
        <TextView
            android:id="@+id/statusIcon"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:gravity="center"
            android:text="✓"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/success_color"
            android:background="@drawable/circle_background"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <!-- 日期和页面信息 -->
        <TextView
            android:id="@+id/titleText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="8dp"
            android:text="周一 - 第1页"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            app:layout_constraintEnd_toStartOf="@id/playButton"
            app:layout_constraintStart_toEndOf="@id/statusIcon"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 录音开始时间 -->
        <TextView
            android:id="@+id/startTimeText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="8dp"
            android:text="2023-12-01 14:30:22"
            android:textSize="14sp"
            android:textColor="@color/text_secondary"
            app:layout_constraintEnd_toStartOf="@id/playButton"
            app:layout_constraintStart_toEndOf="@id/statusIcon"
            app:layout_constraintTop_toBottomOf="@id/titleText" />

        <!-- 录音时长 -->
        <TextView
            android:id="@+id/durationText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="2dp"
            android:layout_marginEnd="8dp"
            android:text="时长: 02:35"
            android:textSize="12sp"
            android:textColor="@color/text_hint"
            app:layout_constraintEnd_toStartOf="@id/playButton"
            app:layout_constraintStart_toEndOf="@id/statusIcon"
            app:layout_constraintTop_toBottomOf="@id/startTimeText" />

        <!-- 播放按钮 -->
        <ImageButton
            android:id="@+id/playButton"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@drawable/circle_background"
            android:contentDescription="@string/play_recording"
            android:src="@drawable/ic_play"
            android:scaleType="centerInside"
            android:enabled="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>
