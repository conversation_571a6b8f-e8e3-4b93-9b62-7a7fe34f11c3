R_DEF: Internal format may change without notice
local
anim slide_in_left
anim slide_in_right
anim slide_out_left
anim slide_out_right
color accent_color
color background_dark
color background_light
color black
color button_disabled
color button_enabled
color button_playing
color button_recording
color icon_disabled
color icon_enabled
color primary_color
color primary_dark_color
color purple_200
color purple_500
color purple_700
color success_color
color teal_200
color teal_700
color text_hint
color text_primary
color text_secondary
color white
dimen control_bar_height
dimen control_bar_height_landscape
dimen control_bar_padding
dimen control_bar_padding_horizontal
dimen control_bar_padding_landscape
dimen control_button_size
dimen control_button_size_landscape
dimen recording_card_margin
dimen recording_card_margin_landscape
dimen recording_card_padding
dimen recording_card_padding_landscape
dimen recording_icon_size
dimen recording_icon_size_landscape
drawable circle_background
drawable ic_arrow_back
drawable ic_calendar
drawable ic_history
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_mic
drawable ic_navigate_before
drawable ic_navigate_before_selector
drawable ic_navigate_next
drawable ic_navigate_next_selector
drawable ic_pause
drawable ic_play
drawable ic_volume_up
drawable rounded_background
id action_main_to_detail
id action_main_to_recording_list
id backButton
id bottomControlBar
id contentLayout
id daysGrid
id detailFragment
id durationText
id emptyStateLayout
id fridayButton
id imageView
id mainFragment
id mondayButton
id nav_graph
id nav_host_fragment
id nextButton
id pageIndicator
id placeholderLayout
id playButton
id previousButton
id recordButton
id recordingListButton
id recordingListFragment
id recordingOverlay
id recordingRecyclerView
id recordingTimeText
id saturdayButton
id startTimeText
id statusIcon
id thursdayButton
id titleText
id tuesdayButton
id viewPager
id wednesdayButton
layout activity_main
layout fragment_detail
layout fragment_main
layout fragment_recording_list
layout item_image_page
layout item_recording
mipmap ic_launcher
mipmap ic_launcher_round
navigation nav_graph
string app_name
string back
string cancel
string duration_format
string error_file_not_found
string error_permission_required
string error_playback_failed
string error_recording_failed
string friday
string grant_permission
string loading_recordings
string long_press_to_record
string monday
string next
string no_audio_file
string no_recordings
string no_recordings_hint
string page_indicator
string pause
string pause_recording
string permission_audio_message
string permission_audio_title
string permission_denied
string permission_storage_message
string permission_storage_title
string play
string play_recording
string playback_completed
string playback_error
string playback_paused
string playing_title
string previous
string record
string recording
string recording_list
string recording_saved
string recording_too_short
string saturday
string sunday
string thursday
string tuesday
string wednesday
style ControlButtonStyle
style MainButtonStyle
style MainButtonStyleLandscape
style PlayButtonStyle
style RecordButtonStyle
style Theme.IntensiveReading
xml backup_rules
xml data_extraction_rules
