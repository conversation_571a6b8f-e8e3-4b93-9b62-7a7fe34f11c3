<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_main" modulePackage="com.intensivereading.app" filePath="app\src\main\res\layout\fragment_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_main_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="127" endOffset="51"/></Target><Target id="@+id/contentLayout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="11" startOffset="4" endLine="125" endOffset="55"/></Target><Target id="@+id/daysGrid" view="GridLayout"><Expressions/><location startLine="22" startOffset="8" endLine="102" endOffset="20"/></Target><Target id="@+id/mondayButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="37" startOffset="12" endLine="45" endOffset="37"/></Target><Target id="@+id/tuesdayButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="48" startOffset="12" endLine="56" endOffset="37"/></Target><Target id="@+id/wednesdayButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="59" startOffset="12" endLine="67" endOffset="37"/></Target><Target id="@+id/thursdayButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="70" startOffset="12" endLine="78" endOffset="37"/></Target><Target id="@+id/fridayButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="81" startOffset="12" endLine="89" endOffset="37"/></Target><Target id="@+id/saturdayButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="92" startOffset="12" endLine="100" endOffset="37"/></Target><Target id="@+id/recordingListButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="105" startOffset="8" endLine="123" endOffset="64"/></Target></Targets></Layout>